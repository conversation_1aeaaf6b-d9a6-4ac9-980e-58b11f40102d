<template>
  <a-drawer :title="title" :bodyStyle="bodyStyle" width="50%" :visible="visible" @close="onClose" :maskClosable="false" destroyOnClose>
    <div class="detail-drawer-wrap">
      <div class="detail-drawer-body-wrap">
        <a-form-model ref="ref_model_form" :model="modelData" :label-position="'right'">
<!--          <a-form-model-item v-show="title !== '新建文档'" v-if="hasSubModel" :label="$t('txt_type')" prop="activeModle" :rules="{-->
          <a-form-model-item   :label="$t('txt_type')" prop="activeModle" :rules="{
              required: true,
              message: $t('msg_select'),
              trigger: 'change',
            }">
            <a-select v-model.trim="modelData.activeModle" allowClear :placeholder="$t('msg_select')" @change="onchangeModel">
              <a-select-option v-for="item in subModelOptions" :key="item.name" :value="item.name">
                {{ $t(item.name) }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('txt_position_flex')" prop="activeCatalog" :rules="{
              required: true,
              message: $t('txt_select_position'),
              trigger: 'change',
            }">
            <div>
              <location-tree ref="ref_locationTree" :disabled="paramsData.locationInfo.disabled" :locationInfo.sync='paramsData.locationInfo' :activeCatalog.sync='modelData.activeCatalog'></location-tree>
            </div>
          </a-form-model-item>
        </a-form-model>
        <jw-layout-builder v-if="layoutVisible && modelData.activeModle" ref="ref_appBuilder" type="Model" :layoutName="modelInfo.layoutName" :modelName="modelData.activeModle" :instanceData="instanceData" @initModel="getModelInfo">
          <template v-for="(node, slotName) in $scopedSlots" :slot="slotName" slot-scope="slotData">
            <slot :name="slotName" v-bind="slotData"></slot>
          </template>
          <template #number="{formData}">
            <a-input value="" disabled v-if="formData.source==='银河自研'||(formData.extensionContent&&formData.extensionContent.source==='银河自研')"></a-input>
            <a-input v-model="formData.number" allowClear v-else></a-input>
          </template>

          <template #deliveryOid="{ formData }">
            <a-tree-select v-model="formData.extensionContent.deliveryOid" :treeData="deliverTree" treeDefaultExpandAll show-search
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" allowClear :filterTreeNode="filterTreeNode">

              <div slot="title" slot-scope="{ name, modelDefinition, modelIcon }">
                <div
                  :class="['name-con', modelDefinition === 'Structure' || modelDefinition === 'Category' ? 'name-con-small' : 'name-con-big']">
                  <jwIcon :type="modelIcon"></jwIcon>
                  <div class="name-item">
                    {{ name }}
                  </div>

                </div>
              </div>
            </a-tree-select>
          </template>
        </jw-layout-builder>
      </div>
    </div>
    <div class="detail-drawer-foot-wrap">
      <a-button type="primary" v-if="
          $refs.ref_appBuilder &&
          $refs.ref_appBuilder.getValue() &&
          $refs.ref_appBuilder.getValue().genericType === 'variable'
        " :loading="saveLoading" @click="onSave(false, true)">{{ $t("btn_over_effectivity") }}</a-button>
      <a-button type="primary" :loading="saveLoading" @click="onSave(true)">{{ $t("btn_over_next") }}
      </a-button>
      <a-button class="btn-cancel" :loading="saveLoading" @click="onSave(false)">{{ $t("btn_done") }}</a-button>
    </div>
    <a-modal :title="$t('txt_materials')" width="65%" :visible="visibleMaterals" :okText="$t('txt_continue_create')" :cancelText="$t('txt_create_cancel')" @ok="confirmMaterals" @cancel="cancelMaterals" :confirmLoading="confirmLoading">
      <materialsObject ref="materialsObjectS" :titleShow="false" :createPartList="createPartMaterialsList" />
    </a-modal>
  </a-drawer>
</template>

<script>
import { jwLayoutBuilder } from "jw_frame";
import { findItem } from "utils/util";
import locationTree from "./location-tree";
import modelStore from "jw_stores/common";
import ModelFactory from "jw_apis/model-factory";
import materialsObject from "views/product/contentManagement/workflow/form-task/similar-materials-task/materials-object.vue";

// 获取子类型
const fetchSubModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/query-sub-model`,
  method: "post"
});

// 查询容器详情
const fetchContainerDetail = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/container/product/findDetail`,
  method: "get"
});

// 获取模型绑定的分类列表
const fetchClassifyList = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.docMicroServer
  }/documentTemplate/find/documentTemplateByRootOid`,
  method: "get"
});

// 获取文档模板列表
const fetchDocumentTemplate = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.docMicroServer
  }/documentTemplate/find/documentTemplateByClsOid`,
  method: "get"
});

// 获取下载和预览按钮权限
const fetchBtnFilter = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.sysconfigServer
  }/preferences/setting/query-config-value`,
  method: "get"
});

// 判断物料去重
const findSamePartData = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/findSamePartData`,
  method: "post"
});

const fetchDeliveryFuzzy = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/delivery/querySelectDeliveryTree`,
  method: 'get',
});

export default {
  props: {
    objectDetailsData: {
      type: Object,
      default: () => ({})
    },
  },
  components: {
    jwLayoutBuilder,
    materialsObject,
    locationTree
  },
  data() {
    this._initInstanceData = {};
    return {
      visible: false,
      layoutVisible: true,
      bodyStyle: { padding: 0 },
      title: "",
      saveLoading: false,
      instanceData: {},
      modelInfo: {},
      deliverTree:[],
      paramsData: {
        locationInfo: {}
      },

      hasSubModel: false,
      subModelOptions: [],

      modelData: {
        activeModle: undefined,
        activeCatalog: undefined
      },
      currentContainerType: "",
      visibleMaterals: false,
      confirmLoading: false,
      showMaterals: false,
      toeffectivityMaterals: false,
      paramsMaterals: {},
      createPartMaterialsList: [],
      btnType: "",
      //交付清单树
    };
  },
  mounted() {
  },
  methods: {
    filterTreeNode(inputVal, treeNode) {
      const self = this; // 保存this指向

      // 递归向上查找祖先节点是否匹配，visitedOids 用于防止死循环
      function isAncestorMatch(dataRef, visitedOids = new Set()) {
        if (!dataRef) return false;
        if (visitedOids.has(dataRef.oid)) return false; // 防止环
        visitedOids.add(dataRef.oid);

        const name = dataRef.name || '';
        if (name.indexOf(inputVal) !== -1) return true;
        const parentOid = dataRef.parentOid;
        if (!parentOid) return false;
        // 递归查找父节点
        const findParentNode = (nodes, oid) => {
          for (const n of nodes) {
            if (n.oid === oid) return n;
            if (n.children && n.children.length > 0) {
              const found = findParentNode(n.children, oid);
              if (found) return found;
            }
          }
          return null;
        };
        const parentNode = findParentNode(self.deliverTree, parentOid);
        return isAncestorMatch(parentNode, visitedOids);
      }

      // 入口传入 dataRef
      return isAncestorMatch(treeNode.data.props.dataRef);
    },
    fetchSubModel(options) {
      console.log("this.objectDetailsData", this.objectDetailsData);
      console.log("this.instanceData", this.instanceData);
      console.log("options", options);
      fetchSubModel
        .execute({
          viewCode: "ENTITY_FILTER",
          objectType: this.modelInfo.modelName,
          // 处理在详情里 已存在type导致参数错误  如果存在containerType表示当前处于详情页
          //containerType 详情的containerType    ||    type  容器的containerType
          //contextType: this.objectDetailsData.containerType || this.$route.query.type,
          //同理
          //id  容器的contextOid    ||    contextOid  详情的contextOid
          //contextOid: this.objectDetailsData.containerOid || this.$route.query.oid,
          contextType: options.params.locationInfo.catalogType,
          contextOid: options.params.locationInfo.catalogOid
        })
        .then(res => {
          if (res.length == 1) {
            this.modelData.activeModle = res[0].name;
            this.hasSubModel = false;
          } else {
            this.modelData.activeModle =
              this.paramsData.modelDefinition || undefined;
            this.hasSubModel = true;
          }
          // if (this.modelInfo.modelName == 'ECAD')
          // {
          //     const ECAD_TYPE = ['ECAD', 'Datasheet', 'Symbol', 'Encapsulation'];
          //     this.subModelOptions = res.filter(model => ECAD_TYPE.includes(model.code));
          // }
          // else
          this.subModelOptions = res.filter(row => {
            return !["Part", "ECAD", "Document"].includes(row.code);
          });

          if (this.subModelOptions.length > 0) {
            this.modelData.activeModle = this.subModelOptions[0].name;
          }
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },
    loadDeliver(){
      fetchDeliveryFuzzy.execute({
        containerOid: this.$route.query.oid,
      }).then(resp => {
        this.deepData(resp)
        this.deliverTree = resp
      })
    },

    deepData(data, onlyDirectChildren = false) {
      const scopedSlots = { title: "title" };
      let arr = [];
      const loop = (tree) => {
        tree.forEach(item => {
          // 设置节点的基本属性
          item.key = item.oid;
          item.value = item.oid;
          item.scopedSlots = scopedSlots;
          delete item.root;

          // 禁用父节点的点击选择
          if (item.children && item.children.length > 0) {
            item.disabled = true; // 禁用父节点点击选择
            loop(item.children); // 递归处理子节点
          }
        });
      };
      loop(data);
      return onlyDirectChildren ? arr : data;
    },
    onchangeModel(val) {
      this.instanceData = _.cloneDeep(this._initInstanceData);
      this.$nextTick(() => {
        this.modelData.activeModle = val;
      });
    },

    show(options) {
      if (options) {
        this.title = options.title;
        this.instanceData = options.instanceData || {
          modelName: options.modelInfo.modelName
        };
        this._initInstanceData = JSON.parse(JSON.stringify(this.instanceData));
        this.modelInfo = options.modelInfo;
        this.paramsData = options.params;
        this.modelData.activeCatalog = this.paramsData.locationInfo.catalogOid;
        this.btnType = options.btnType;
      }
      // this.fetchContainerDetail(options.params.locationInfo.containerOid);
      this.fetchSubModel(options);
      this.loadDeliver();
      this.visible = true;
      this.saveLoading = false;
      _.delay(() => {
        this.$refs.ref_model_form && this.$refs.ref_model_form.resetFields();
        this.$refs.ref_appBuilder && this.$refs.ref_appBuilder.clearValidate();
      });
    },
    getModelInfo(data) {
      let appBuilder = this.$refs.ref_appBuilder;
      if (this.objectDetailsData.modelDefinition === "Category") {
        if (data.model && data.model.clsProperty) {
          fetchClassifyList
            .execute({
              rootOid: data.model.clsProperty.oid
            })
            .then(res => {
              if (res && res.length > 0) {
                let item = findItem(res, this.objectDetailsData.clsOid);
                const values = appBuilder.getValue()
                for(const key in values) {
                  this.$set(this.instanceData, key, values[key]);
                }
                this.$set(this.instanceData, "classificationInfo", item);

                if (item) {
                  fetchDocumentTemplate
                    .execute({
                      clsOid: item.oid
                    })
                    .then(data => {
                      if (data && data.length > 0) {
                        this.getBtnFilter(data[0]);
                      }
                    })
                    .catch(err => {
                      this.$error(err.msg);
                    });
                }
              }
            })
            .catch(err => {
              this.$error(err.msg);
            });
        }
      }
    },
    getBtnFilter(docInfo) {
      fetchBtnFilter
        .execute({
          name: "Delivery_Document_Down_Status"
        })
        .then(res => {
          if (res && res.length) {
            let item = res.find(val => val.value === docInfo.lifecycleStatus);
            let btnFilter = item ? true : false;
            this.instanceData = {
              classificationInfo: {
                ...this.instanceData.classificationInfo,
                btnFilter: !btnFilter
              }
            };
          }
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },
    fetchContainerDetail(containerOid) {
      fetchContainerDetail
        .execute({
          oid: containerOid
        })
        .then(res => {
          this.currentContainerType = res.modelDefinition;
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },
    checkModelForm() {
      let modelForm = this.$refs.ref_model_form;
      if (modelForm) {
        modelForm.validate().catch(err => {
          this.$error("modelForm Error....");
        });
      }
    },

    /**
     * toeffectivity: 是否跳转有效性设置页面
     */
    onSave(show, toeffectivity) {
      this.checkModelForm();
      let appBuilder = this.$refs.ref_appBuilder;
      appBuilder &&
        appBuilder.validate().then(() => {
          let params = appBuilder.getValue();
          params.modelDefinition = this.modelData.activeModle;
          params.locationInfo = this.paramsData.locationInfo;
          if (this.paramsData.targetOid) {
            params.targetOid = this.paramsData.targetOid;
          }
          if (this.paramsData.sourceOid) {
            params.sourceOid = this.paramsData.sourceOid;
          }
          if (
            params.source === "银河自研" ||
            (params.extensionContent &&
              params.extensionContent.source === "银河自研")
          ) {
            params.number = "";
          } else if (params.source === "外协类文件" && !params.number) {
            this.$error("请输入编码");
            return false;
          }
          this.saveLoading = true;
          if (this.btnType === "Part") {
            findSamePartData
              .execute([{ ...params }])
              .then(el => {
                if (el && !(JSON.stringify(el) === "{}")) {
                  this.createPartMaterialsList = el.create;
                  this.visibleMaterals = true;
                  this.$nextTick(() => {
                    this.$refs.materialsObjectS.show();
                  });
                  this.toeffectivityMaterals = toeffectivity;
                  this.showMaterals = show;
                  this.paramsMaterals = params;
                } else {
                  this.addCreate(show, toeffectivity, params);
                }
              })
              .catch(err => {
                this.saveLoading = false;
                this.$error(err.msg);
              });
          } else {
            this.addCreate(show, toeffectivity, params);
          }
        });
    },
    addCreate(show, toeffectivity, params) {
      // 检测 Document 类型的 cn_jwis_secrecy 属性值
      if (params.modelName === 'Document' &&
          params.extensionContent &&
          params.extensionContent.cn_jwis_secrecy === '内部') {
        this.saveLoading = false;
        this.confirmLoading = false;
        this.$error('密级 不允许为"内部"');
        return;
      }

      ModelFactory.create({
        url: this.paramsData.url,
        method: "post"
      })
        .execute(params)
        .then(res => {
          this.$success(this.$t("txt_create_success"));

          // 功能修改 之前的类型节点功能合并到分支节点上，类型节点暂时取消
          if (this.objectDetailsData.modelDefinition === "Structure") {
            this.$emit("onHandleDelivery", res);
          } else {
            this.$emit("fetchTable", {
              oid: this.paramsData.locationInfo.catalogOid,
              type: this.paramsData.locationInfo.catalogType,
              clickOid: this.paramsData.targetOid,
              clickType: this.paramsData.targetType
            });
          }
          this.saveLoading = false;
          this.confirmLoading = false;

          if (toeffectivity) {
            Jw.jumpToDetail(res, {
              hasPermission: true,
              toUrl: `/object-details?currentTabName=effectivity`,
              blank: false
            });
          }

          if (!show) {
            this.hide();
          } else {
            this.layoutVisible = false;
            this.$nextTick(() => {
              this.layoutVisible = true;
              //缓存分类信息
              if (params.classificationInfo) {
                this.instanceData.classificationInfo = params.classificationInfo;
              }
            });
          }
        })
        .catch(err => {
          this.saveLoading = false;
          this.confirmLoading = false;
          console.error(err);
          this.$error(err.msg);
        })
        .finally(() => {
          this.saveLoading = false;
          this.confirmLoading = false;
          this.visibleMaterals = false;
          this.showMaterals = false;
          this.toeffectivityMaterals = false;
          this.paramsMaterals = {};
          this.createPartMaterialsList = [];
        });
    },
    hide() {
      this.visible = false;
      this.saveLoading = false;
      this.modelData = {
        activeModle: undefined,
        activeCatalog: undefined
      };
      this.modelInfo = {};
      this.instanceData = {};
      this._initInstanceData = {};
      modelStore.set("operationCode", "");
    },
    onClose() {
      this.hide();
    },

    confirmMaterals() {
      this.addCreate(
        this.showMaterals,
        this.toeffectivityMaterals,
        this.paramsMaterals
      );
      this.saveLoading = false;
      this.confirmLoading = true;
    },
    cancelMaterals() {
      this.saveLoading = false;
      this.confirmLoading = false;
      this.visibleMaterals = false;
      this.showMaterals = false;
      this.toeffectivityMaterals = false;
      this.paramsMaterals = {};
      this.createPartMaterialsList = [];
    }
  }
};
</script>

<style lang="less" scoped>
.detail-drawer-wrap {
  .detail-drawer-body-wrap {
    height: calc(~"100vh - 126px");
  }
}

.name-con {
  display: flex;
  align-items: center;

  .name-item {
      margin: 0 8px;
      cursor: pointer;
      text-overflow: ellipsis;
      white-space: nowrap;
  }
}
</style>
