<template>
    <multipane class="delivery-list-wrap">
        <div class="delivery-panel left-panel">
            <jw-table ref="refTable" :loading="loading" :toolbars="toolbars" :columns="columns" row-id="id"
                :tree-config="{ rowField: 'id', line: true, transform: true, iconOpen: 'jwi-iconshouqi-2', iconClose: 'jwi-iconzhankai-2', lazy: true, expandRowKeys, loadMethod: loadChildrenMethod }"
                :data-source.sync="tableData" :showPage="false" :cell-style="cellStyle" :header-cell-style="headerCellStyle"
                :row-config="{ isCurrent: true, useKey: true }" @onToolInput="onToolInput"
                @current-change="onCurrentChange">

                <template slot="tool-after-end">
                    <div>
                      <a-button @click="batchUpdateToPMS" :loading="batchUpdateLoading" :disabled="!batchUpdatePermission">
                        批量更新到PMS
                      </a-button>
                      <a-button  @click="openUpdate">
                        更新
                      </a-button>
                      <a-button @click="exportExcel" :loading="saveLoading">
                        导出
                      </a-button>
                        <a-button @click="openimport">
                            <jw-icon type="jwi-iconImport" />
                        </a-button>
                        <a-radio-group v-if="isShowLevel" v-model.trim="level" :disabled="radioDisabled" @change="onChangeLevel">
                            <a-radio-button key="All" value="All">All</a-radio-button>
                            <a-radio-button v-for="item in 4" :key="item" :value="item">{{ item }}</a-radio-button>
                        </a-radio-group>
                    </div>
                </template>
                <template #nodeLevel="{ row }">
                    <span class="level-wrap">{{ row.nodeLevel }}</span>
                </template>
                <template #numberHeaderSlot>
                    <div class="number-header-wrap">
                        <span>{{ $t('txt_delivery_pkg') }}</span>
                        <span :title="$t('txt_add_node')" v-if="structCreate"
                            @click="onOpenCreateModal('Structure', true, {})">
                            <jw-icon type="jwi-iconadd" />
                        </span>
                    </div>
                </template>
                <template #numberSlot="{ row }">
                    <div class="name-wrap">
                        <div
                            :class="['name-con', row.modelDefinition === 'Structure' || row.modelDefinition === 'Category' ? 'name-con-small' : 'name-con-big']">
                            <jwIcon :type="row.modelIcon"></jwIcon>
                            <div v-if="row.modelDefinition === 'Structure' || row.modelDefinition === 'Category'"
                                class="name-item">
                                {{ row.cname || row.name }}
                            </div>
                            <div v-else class="name-item">{{ row.cname || row.name }}，{{ row.number }}</div>
                            <a-tag v-if="row.modelDefinition === 'Structure' || row.modelDefinition === 'Category'">
                                {{ $t(row.lifecycleStatus) }}
                            </a-tag>
                        </div>
                        <div class="btn-con">
                            <a-dropdown placement="bottomRight" :trigger="['click']"
                                @visibleChange="(visible) => getDeliveryInsFilter(visible, row)">
                                <span class="jwi-iconellipsis" style="cursor: pointer;"></span>
                                <a-menu slot="overlay" @click="({ key }) => onOperateClick(key, row)">
                                    <a-spin v-if="row.loading_status == 'loading'" style="margin: 20px 65px;" />
                                    <a-menu-item v-else-if="row.loading_status == 'failed'" style="margin: 20px auto;">
                                        {{ $t("txt_get_failure") }}
                                    </a-menu-item>
                                    <template v-else v-for="item in row.operationList">
                                        <a-menu-item :key="item.code" :disabled="item.status === 'disable'">
                                            <span :class="item.icon" style="margin-right: 8px;"></span>
                                            {{ $t(item.internationalizationKey) }}
                                        </a-menu-item>
                                    </template>
                                </a-menu>
                            </a-dropdown>
                        </div>
                    </div>
                </template>
                <template #statusSlot="{ row }">
                    <a-tag v-if="row.modelDefinition !== 'Structure' &&
                        row.modelDefinition !== 'Category'" color="blue">
                        {{ $t(row.lifecycleStatus) }}
                    </a-tag>
                </template>
            </jw-table>
        </div>
        <multipane-resizer></multipane-resizer>
      <div class="delivery-panel right-panel" v-if="showDetailPanel">
            <basic-info v-if="detailInfo.oid" ref="basicInfo" :detailInfo="detailInfo" @getDocTempInfo="getDocTempInfo"
                @onSearch="reRenderTable"></basic-info>
        </div>
        <div class="no-data-wrap" v-if="tableData.length === 0 && !searchKey">
            <div class="no-data-con">
                <img src="./no-data.gif" alt="" />
                <div>{{ $t('txt_no_delivery') }}
                    <a-button type="link" :disabled="!structCreate" @click="onOpenCreateModal('Structure', true, {})">
                        {{ $t('btn_new_create') }}
                    </a-button>
                    {{ $t('txt_add_c') }}
                </div>
            </div>
        </div>
        <jw-base-color-modal :title="$t('btn_new_create')" :width="600" :visible.sync="visible"
            :ok-btn-loading="nextLoading" :ok-text="$t('btn_over_next')" @ok="onSave('next')" @cancel="onCloseSaveModal">
            <jw-layout-builder v-if="visible" ref="ref_appBuilder" type="Model" :layoutName="layoutName"
                :modelName="modelName" :instanceData="instanceData">
            </jw-layout-builder>
            <template #footer-before-btn>
                <a-button @click="onSave('save')" :loading="saveLoading">{{ $t('btn_done') }}</a-button>
            </template>
        </jw-base-color-modal>
        <operation-dropdown v-show="false" ref="dropdown" :current-record="currentObj" @renderData="reRenderTable">
        </operation-dropdown>
        <create-drawer ref="cerateDrawer" :objectDetailsData="currentObj" @onHandleDelivery="onCreateDoc" @fetchTable="getTableData"></create-drawer>
        <jw-search-engine-modal :title="$t('txt_adding_business_object')" :onlySearchObject="true"
            :fixedContainerOid="fixedContainerOid" :visible.sync="objVisible" :model-list="modelList" @ok="onAddObj"/>
        <!-- 带附件导入导出zip -->
        <batchImportModelFile
            :objInfo="modelObjInfo"
            :visible="importModelFileVisible"
            :currentTree="spectrumInfo"
            @close="importModelFileVisible = false"
            @getList="onSearch"
        >
        </batchImportModelFile>
    </multipane>
</template>

<script>
import { getExcelList } from "views/product-content/content-manage/apis/index"
import batchImportModelFile from "views/product-content/content-manage/batch-import-model-file.vue"
import basicInfo from './basic-info';
import { jwBaseColorModal, jwLayoutBuilder, jwSearchEngineModal } from 'jw_frame';
import createDrawer from '../content-manage/create-drawer';
import operationDropdown from 'components/operation-dropdown';
import { Multipane, MultipaneResizer } from "vue-multipane";
import { generateUUID } from 'views/product-container/object-details/apis.js';
import { getCookie } from 'jw_utils/cookie';
import { getParent } from 'utils/util.js';
import ModelFactory from 'jw_apis/model-factory';

const modelMapviewCode = Jw.modelMapviewCode;

//交付清单导出
const exportExcel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/deliveryReport/exportExcel`,
  method: 'post',
  responseType: "blob"
});

// 批量更新PDM交付清单到PMS中预期文档的状态
const batchUpdateToPMSApi = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.productDeliveryServer}/delivery/batchUpdateToPMS`,
    method: 'post',
});

// 查询交付清单是否有添加权限
const fetchDeliveryFilter = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
    method: 'post',
});

// 文件夹目录
const fetchfolderTree = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/folder/searchTree`,
    method: "get",
});

// 获取容器交付清单
const fetchDeliveryTree = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.productDeliveryServer}/delivery/findNodesByCatalogOid`,
    method: 'post',
});

// 模糊查询
const fetchDeliveryFuzzy = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.productDeliveryServer}/delivery/structureTree/Fuzzy`,
    method: 'post',
});

// 加载子节点
const findChildNode = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.productDeliveryServer}/delivery/findChildrenByDeliveryOid`,
    method: 'get',
});

// 创建节点
const createDelivery = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.productDeliveryServer}/delivery/create`,
    method: 'post',
});

// 删除节点
const deleteDelivery = (oid) => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.productDeliveryServer}/delivery/delete?oid=${oid}`,
    method: 'post',
});

// 获取可添加的实例对象类型
const fetchInstanceType = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.sysconfigServer}/preferences/setting/query-config-value`,
    method: 'get',
});

// 添加实例对象
const batchAddInstance = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.productDeliveryServer}/delivery/batchAddInstance`,
    method: 'post',
});

// 删除实例对象
const batchDeleteInstance = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.productDeliveryServer}/delivery/batchDeleteInstance`,
    method: 'post',
});

const permissionApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
  method: "post"
});


export default {
    name: 'deliveryList',
    components: {
        basicInfo,
        jwBaseColorModal,
        jwLayoutBuilder,
        jwSearchEngineModal,
        createDrawer,
        operationDropdown,
        Multipane,
        MultipaneResizer,
        batchImportModelFile
    },
    props: {
        spectrumInfo: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
          hasPermission: false,
          showDetailPanel: false,

            structCreate: false,
            // catalogCreate: false,
            containerInfo: {},
            searchKey: '',
            level: 'All',
            isShowLevel: true,
            radioDisabled: false,
            loading: false,
            expandRowKeys: [],
            tableData: [],
            currentObj: {},
            detailInfo: {},
            saveObj: {},
            visible: false,
            nextLoading: false,
            saveLoading: false,
            batchUpdateLoading: false,
            batchUpdatePermission: false,
            layoutName: 'create',
            modelName: 'Structure',
            isRootNode: null,
            instanceData: {},
            visibleStatus: false,
            statusData: [],
            objVisible: false,
            fixedContainerOid: '',
            modelList: [],
            docTempInfo: {},
            nodeParams:{},

            modelObjInfo: {},
            importModelFileVisible: false,
        };
    },
    computed: {
        toolbars() {
            return [
                {
                    name: this.$t('btn_search'),
                    position: 'before',
                    display: 'input',
                    value: this.searchKey,
                    allowClear: true,
                    placeholder: this.$t('search_text'),

                    key: 'search',
                },
            ]
        },
        columns() {
            return [
                {
                    field: 'nodeLevel',
                    title: '',
                    width: 14,
                    // showOverflow: false,
                    align: 'right',
                    resizable: false,
                    params: {
                        showHeaderMore: false,
                    },
                    slots: {
                        default: 'nodeLevel',
                    },
                },
                {
                    field: 'number',
                    title: this.$t('txt_delivery_pkg'),
                    minWidth: 400,
                    treeNode: true,
                    slots: {
                        default: 'numberSlot',
                        header: 'numberHeaderSlot',
                    },
                },
                {
                    field: 'displayVersion',
                    title: this.$t('txt_version'),
                    width: 120,
                    cellRender: {
                        name: 'tag',
                    },
                },
                {
                    field: 'lifecycleStatus',
                    title: this.$t('txt_plan_lifecycle'),
                    width: 120,
                    slots: {
                        default: 'statusSlot',
                    },
                },
            ]
        },
    },
    mounted() {
        this.init();
        this.getInstanceType();
    },
    methods: {
      openUpdate(){
        this.modelObjInfo = getExcelList("deliverUpdate")
        this.importModelFileVisible = true
      },
      exportExcel() {
        this.saveLoading = true; // 开始加载
        // 调用后端接口导出 Excel 文件
        exportExcel.execute({
          searchKey: '',
          catalogOid: this.spectrumInfo.oid ? this.spectrumInfo.oid : this.$route.query.oid,
          catalogType: this.spectrumInfo.oid ? this.spectrumInfo.type : this.$route.query.type,
          level: null
        }).then(resp => {
          this.saveLoading = false; // 加载完成
          if (resp.type === 'application/json') {
            var reader = new FileReader();
            reader.readAsText(resp, 'utf-8');
            reader.onload = (e) => {
              let readerres = reader.result;
              let parseObj = JSON.parse(readerres);
              this.$error(parseObj.msg)
            }
          } else {
            let url = window.URL.createObjectURL(new Blob([resp]))
            let link = document.createElement('a')
            let fileName = "交付清单导出.xlsx"
            link.href = url
            link.style.display = 'none'
            link.setAttribute('download', fileName)
            document.body.appendChild(link)
            link.click()
          }
        })
      },
      // 批量更新PDM交付清单到PMS中预期文档的状态
      batchUpdateToPMS() {
        this.$confirm({
          title: '确认批量更新',
          content: '确定要批量更新PDM交付清单到PMS中预期文档的状态吗？',
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            this.batchUpdateLoading = true;

            // 构建请求参数
            const params = {
              catalogOid: this.spectrumInfo.oid ? this.spectrumInfo.oid : this.$route.query.oid,
              catalogType: this.spectrumInfo.oid ? this.spectrumInfo.type : this.$route.query.type,
              searchKey: this.searchKey,
              level: this.searchKey || this.level === 'All' ? null : this.level,
            };

            // 调用后端接口
            batchUpdateToPMSApi.execute(params).then(resp => {
              this.batchUpdateLoading = false;
              this.$success('批量更新成功！');
              // 刷新表格数据
              this.onSearch();
            }).catch(err => {
              this.batchUpdateLoading = false;
              this.$error(err.msg || '批量更新失败');
            });
          }
        });
      },
        //打开导入弹窗
        openimport(){
            this.modelObjInfo = getExcelList("deliverImport")
            this.importModelFileVisible = true
        },
        init() {
            this.getDeliveryFilter();
            this.getfolderTree();
            this.onSearch();
        },
        onSearch() {
            this.getTableData();
        },
        getDeliveryFilter() {
            let params = {};
            if (this.spectrumInfo.type === 'ProductCatalog') {
                params = {
                    viewCode: 'DELIVERYOPERATION',
                }
            } else {
                let { oid } = this.spectrumInfo.oid ? this.spectrumInfo : this.$route.query;
                params = {
                    viewCode: 'DELIVERYCONTAINEROPERATION',
                    objectType: 'Container',
                    objectOid: oid,
                };
            }
            fetchDeliveryFilter.execute(
                params
            ).then((res) => {
                if (res && res.length) {
                    res.forEach(item => {
                        this[item.code] = item.status === 'enable' ? true : false;
                        // 设置批量更新到PMS的权限
                        if (item.code === 'catalogCreate') {
                            this.batchUpdatePermission = item.status === 'enable';
                        }
                    })
                }
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        getDeliveryInsFilter(visible, row) {
            if (visible) {
                this.$set(row, 'loading_status', 'loading');
                if (row.modelDefinition !== 'Structure' && row.modelDefinition !== 'Category') {
                    let tree = this.$refs.refTable.getTableData().fullData;
                    let parent = getParent(tree, row.id, 'id');
                    fetchDeliveryFilter.execute({
                        viewCode: 'DELIVERYINSTANCE',
                        objectOid: parent.oid,
                        objectType: parent.type,
                    }).then((res) => {
                        if (res && res.length) {
                            let addObject = res.find(val => val.code === 'addObject');
                            let addObjectFilter = addObject.status === 'enable' ? true : false;
                            let arr = res.filter(item => {
                                return item.code === 'delete';
                            });
                            // arr[0].status = addObjectFilter && row.clsOid !== row.parentClsOid ? arr[0].status : 'disable';
                            if (this.spectrumInfo.type === 'ProductCatalog') {
                                arr[0].status = 'disable';
                            }
                            this.$set(row, 'operationList', arr);
                            this.$set(row, 'loading_status', 'done');
                        }
                    }).catch((err) => {
                        this.$error(err.msg);
                    });
                } else {
                    fetchDeliveryFilter.execute({
                        viewCode: 'DELIVERYINSTANCE',
                        objectOid: row.oid,
                        objectType: row.type,
                    }).then((res) => {
                        if (res && res.length) {
                            let arr = res.filter(item => {
                              if (item.code === 'edit' || item.code === 'delete') {
                                return true;
                              } else {
                                return item.categoryType === row.modelDefinition;
                              }
                              // 功能合并  类型节点上的功能合并到分支节点上
                              if (item.code !== 'read') {
                                if (item.code !== 'download') {
                                  return true;
                                }
                              }
                            });

                            this.$set(row, 'operationList', arr);
                            this.$set(row, 'loading_status', 'done');
                        }
                    }).catch((err) => {
                        this.$error(err.msg);
                        this.$set(row, 'loading_status', 'failed');
                    });
                }
            }
        },
        getfolderTree() {
            let { oid, masterType, modelDefinition, containerModel } = this.spectrumInfo.oid ? this.spectrumInfo : this.$route.query;
            let params = {
                containerOid: oid,
                containerModel: masterType || modelDefinition || containerModel,
            };
            fetchfolderTree.execute(
                params
            ).then((res) => {
                if (res && res.length) {
                    this.containerInfo = res[0];
                } else {
                    this.containerInfo = {};
                }
            }).catch(err => {
                this.$error(err.msg);
            })
        },
        getInstanceType() {
            fetchInstanceType.execute({
                name: 'Delivery_Add_Instance_Type',
            }).then((res) => {
                if (res && res.length) {
                    this.modelList = res.map(item => {
                        item.code = item.value;
                        return item;
                    });
                  // 找到document的索引
                  let documentIndex = this.modelList.findIndex(item => item.name === 'Document');
                  // 如果找到了document
                  if (documentIndex !== -1) {
                    // 删除document
                    let [removed] = this.modelList.splice(documentIndex, 1);
                    // 在第0位插入
                    this.modelList.splice(0, 0, removed);
                  }
                } else {
                    this.modelList = [
                        {
                            name: this.$t('txt_part'),
                            code: 'PartIteration',
                        },
                    ];
                }
            }).catch(err => {
                this.$error(err.msg);
            })
        },
        timeAscOrder(data, p) {
            for (let i = 0; i < data.length - 1; i++) {
                for (let j = 0; j < data.length - 1 - i; j++) {
                    if (data[j][p] > data[j + 1][p]) {
                        var temp = data[j];
                        data[j] = data[j + 1];
                        data[j + 1] = temp;
                    }
                }

                if (data[i].children && data[i].children.length > 0) {
                    data[i].children = this.timeAscOrder(data[i].children, p)
                }
            }

            return data;
        },
        getTableData() {
            this.loading = true;
            fetchDeliveryFuzzy.execute({
                searchKey: this.searchKey,
                catalogOid: this.spectrumInfo.oid ? this.spectrumInfo.oid : this.$route.query.oid,
                catalogType: this.spectrumInfo.oid ? this.spectrumInfo.type : this.$route.query.type,
                level: this.searchKey || this.level === 'All' ? null : this.level,
            }).then(resp => {
                let res = this.timeAscOrder(resp, 'createDate');
                generateUUID(res);
                this.setInitStatus(res, 0);
                if (res.length > 0) {
                    this.tableData = res;
                    this.showDetailPanel = true; // 显示右侧面板
                    this.currentObj = res[0];
                    this.detailInfo = res[0];
                    this.$nextTick(() => {
                        this.$refs.refTable.setCurrentRow(res[0]);
                        this.$refs.basicInfo.getObjDetail();
                    })
                } else {
                    this.tableData = []
                    this.currentObj = {};
                    this.detailInfo = {};
                    if (this.searchKey) this.$warning(this.$t('txt_no_data'));
                }
                this.loading = false;
            }).catch(err => {
                this.loading = false;
                this.isShowLevel = true;
                this.radioDisabled = false;
                this.searchKey = '';
                this.$error(err.msg);
            })
        },
        loadChildrenMethod({ row }) {
            return findChildNode.execute({
                oid: row.oid,
            }).then(resp => {
                let res = this.timeAscOrder(resp, "createDate");
                if (!res || !res.length) {
                    row.hasChild = false;
                }
                generateUUID(res);
                this.setInitStatus(res, row.nodeLevel + 1, row);
                return res;
            }).catch(err => {
                this.$error(err.msg);
            })
        },
        setInitStatus(tree, rowLevel, row) {
            const loop = (data, level) => {
                data.forEach(val => {
                    val.nodeLevel = level;
                    let parent = {};
                    if (row && row.oid) {
                        parent = { ...row };
                    } else {
                        parent = getParent(tree, val.id, 'id');
                    }
                    val.parentClsOid = parent && parent.clsOid ? parent.clsOid : '';
                    if (!val.children) {
                        val.children = [];
                    }
                    if (level >= this.level) {
                        val.hasChild = true;
                    } else {
                        this.expandRowKeys.push(val.id);
                    }
                    if (val.children instanceof Array) {
                        loop(val.children, level + 1);
                    }
                })
            }
            loop(tree, rowLevel);
        },
        cellStyle({ column }) {
            if (column.property === 'nodeLevel') {
                return {
                    borderRight: 0,
                }
            }
        },
        headerCellStyle({ column }) {
            if (column.property === 'nodeLevel') {
                return {
                    borderRight: 0,
                    borderTop: '1px solid #e8eaec',
                    borderBottom: '1px solid #e8eaec',
                }
            }
            return {
                borderTop: '1px solid #e8eaec',
                borderBottom: '1px solid #e8eaec',
            }
        },
        onToolInput({ key }, value) {
            if (key === 'search') {
                this.searchKey = value;
                if (!this.searchKey) {
                    this.level = 1;
                    this.radioDisabled = false;
                } else {
                    this.level = 'All';
                    this.radioDisabled = true;
                }
                this.onSearch();
            }
        },
        onChangeLevel(e) {
            this.level = e.target.value;
            this.onSearch();
        },
        async onCurrentChange({row}) {

          // 根据需要选择特定属性进行判断
          if (row.modelDefinition === 'JWIGeneralDocument') {
            // 执行权限检查
            this.hasPermission = await this.checkPermission(row);
            console.log("是否有权限：" + this.hasPermission);
            // 如果没有权限，隐藏右侧面板并返回
            if (!this.hasPermission) {
              this.$error("无查看权限！");
              this.showDetailPanel = false; // 隐藏右侧面板
              this.detailInfo = null; // 清空详情信息
              return;
            }
          }
          // 权限校验通过或不需要校验的行，更新状态
          this.showDetailPanel = true; // 显示右侧面板
          this.currentObj = { ...row };
          this.detailInfo = { ...row };

          this.$nextTick(() => {
            this.$refs.basicInfo.getObjDetail();
            this.$refs.basicInfo.onCancelEdit(false);
          });
        },
        onOperateClick(key, row) {
            switch (key) {
                case 'structCreate':
                    this.onOpenCreateModal('Structure', false, row);
                    break;
                case 'catalogCreate':
                    this.onOpenCreateModal('Category', false, row);
                    break;
                case 'setStatus':
                    this.onOpenStatusModal(row);
                    break;
                case 'addObject':
                    this.onOpenAddObjModal(row);
                    break;
                case 'edit':
                    this.$refs.basicInfo.onEdit();
                    break;
                case 'createDoc':
                    this.onOpenCreateDocModal(row);
                    break;
                case 'delete':
                    this.onDelete(row);
                    break;
                default:
                    break;
            }
        },
        onOpenCreateModal(type, isRoot, row) {
            this.visible = true;
            this.modelName = type;
            this.instanceData.modelName = type;
            this.isRootNode = isRoot;
            this.currentObj = { ...row };
        },
        onSave(flag) {
            let appBuilder = this.$refs.ref_appBuilder;
            console.log(" appBuilder", appBuilder.validate())
            console.log(" appBuilder2", appBuilder)
            appBuilder.validate().then(() => {
                this[flag + 'Loading'] = true;
                let params = appBuilder.getValue();
                if (this.isRootNode) {
                    this.currentObj = {};
                }
                if (flag === 'next') {
                    this.saveObj = { ...this.currentObj };
                }
                params.parentOid = this.saveObj.oid ? this.saveObj.oid : this.currentObj.oid;
                params.tenantOid = getCookie('tenantOid'),
                    params.root = this.isRootNode;
                params.modelDefinition = this.modelName;
                params.locationInfo = {
                    catalogOid: this.spectrumInfo.oid ? this.spectrumInfo.oid : this.$route.query.oid,
                    catalogType: this.spectrumInfo.oid ? this.spectrumInfo.type : this.$route.query.type,
                    containerOid: this.spectrumInfo.oid ? this.spectrumInfo.oid : this.$route.query.oid,
                    containerType: this.spectrumInfo.oid ? this.spectrumInfo.type : this.$route.query.type,
                    containerModelDefinition: this.spectrumInfo.oid ? this.spectrumInfo.modelDefinition : this.$route.query.modelDefinition,
                }
                createDelivery.execute(
                    params
                ).then((res) => {
                    this[flag + 'Loading'] = false;
                    if (flag === 'next') {
                        this.$refs.ref_appBuilder && this.$refs.ref_appBuilder.resetFields();
                        this.$refs.ref_appBuilder && this.$refs.ref_appBuilder.clearValidate();
                        // 清空数据
                        // this.instanceData = {};

                      // 使用前端自带的清除功能
                      const closeBus = document.querySelectorAll('.anticon-close-circle')
                      closeBus.forEach(item => item.click())
                    } else {
                        this.onCloseSaveModal();
                    }
                    this.reloadTreeExpand();
                }).catch((err) => {
                    console.error(err)
                    this[flag + 'Loading'] = false;
                    this.$error(err.msg);
                });
            })
        },
        onCloseSaveModal() {
            this.visible = false;
            this.saveObj = {};
            this.init();
        },
        onDelete(row) {
            this.currentObj = { ...row };
            this.$confirm({
                title: this.$t('msg_confirm_delete'),
                okText: this.$t('btn_ok'),
                cancelText: this.$t('btn_cancel'),
                onOk: () => {
                    if (row.type === 'Delivery') {
                        return deleteDelivery(row.oid).execute(

                        ).then((res) => {
                            this.$success(this.$t('txt_remove_success'));
                            this.getfolderTree();
                            this.reRenderTable();
                            this.init();
                        }).catch((err) => {
                            this.$error(err.msg);
                        });
                    } else {
                        let tree = this.$refs.refTable.getTableData().fullData;
                        let parent = getParent(tree, row.id, 'id');
                        let params = {
                            deliveryOid: parent.oid,
                            secObjList: [{
                                masterOid: row.masterOid,
                                masterType: row.masterType,
                                modelDefinition: row.modelDefinition,
                            }]
                        }
                        return batchDeleteInstance.execute(
                            params
                        ).then((res) => {
                            this.$success(this.$t('txt_remove_success'));
                          // 获取被删除节点的父节点
                          const refTable = this.$refs.refTable;
                          const fullData = refTable.getTableData().fullData;
                          const parent = getParent(fullData, this.currentObj.id, 'id');

                          if (parent) {
                            parent.hasChild = true;
                            refTable.reloadTreeExpand(parent); // 刷新父节点的数据
                            refTable.setCurrentRow(parent); // 选中父节点
                          }
                            // this.init();
                            // this.reRenderTable();
                        }).catch((err) => {
                            this.$error(err.msg);
                        });
                    }
                },
            });
        },
        onOpenStatusModal(row) {
            this.currentObj = { ...row };
            this.$nextTick(() => {
                this.$refs.dropdown.rowOperation('setStatus');
            })
        },
        onOpenAddObjModal(row) {
            this.objVisible = true;
            this.currentObj = { ...row };
            this.fixedContainerOid = row.catalogOid;
        },
        onAddObj(selectedRows) {
            if (selectedRows.length === 0) {
                this.$warning(this.$t('txt_please_seleted_data'));
                return;
            }
            let params = {
                deliveryOid: this.currentObj.oid,
            }
            let selected = [];
            selectedRows.forEach(item => {
                let temp = {
                    masterOid: item.masterOid,
                    masterType: item.masterType,
                    modelDefinition: item.modelDefinition,
                }
                selected.push(temp);
            })
            params.secObjList = selected;
            batchAddInstance.execute(
                params
            ).then((res) => {
                this.$success(this.$t('msg_save_success'));
                this.reloadTreeExpand();
                this.objVisible = false;
                // 在添加成功后，重新刷新树结构，防止界面不展示
                // this.init();
                // this.reRenderTable();
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        getDocTempInfo(data) {
            if (data.secondaryFile && data.secondaryFile.length > 0) {
                data.primaryFile = [data.secondaryFile[0]];
                if (data.levelForSecrecy === 0) {
                    data.levelForSecrecy = null;
                }
                delete data.secondaryFile;
                delete data.type;
            }
            this.docTempInfo = data;
        },
        onOpenCreateDocModal(row) {
            this.currentObj = { ...row };
            this.$nextTick(() => {
                this.$refs.cerateDrawer.show({
                    title: this.$t('btn_new_create'),
                    modelInfo: {
                        layoutName: 'create',
                        modelName: 'Document',
                    },
                    instanceData: {
                        ...this.docTempInfo,
                        modelName: 'Document',
                    },
                    params: {
                        url: `${Jw.gateway}/${Jw.docMicroServer}/document/create`,
                        locationInfo: {
                            catalogOid: this.containerInfo.oid,
                            catalogType: this.containerInfo.type,
                            containerOid: row.containerOid,
                            containerType: row.containerType,
                            containerModelDefinition: row.containerModelDefinition,
                        },
                    },
                });
            })
        },
        onCreateDoc(res) {
            this.onAddObj([res]);
        },
        reloadTreeExpand() {
            if (this.tableData.length > 0) {
                const refTable = this.$refs.refTable;
                const row = refTable.getRowById(this.currentObj.id);
                if (row) {
                    row.hasChild = true;
                    refTable.reloadTreeExpand(row);
                    refTable.setCurrentRow(row);
                } else {
                    this.getfolderTree();
                    this.onSearch();
                }
            } else {
                this.init();
            }
        },
        reRenderTable() {
            const refTable = this.$refs.refTable;
            const fullData = refTable.getTableData().fullData;
            const parent = getParent(fullData, this.currentObj.id, 'id') || {};
            if (this.currentObj.root) {
                this.onSearch();
            } else if (parent) {
                if (parent.root) {
                    this.onSearch();
                }
                parent.hasChild = true;
                refTable.reloadTreeExpand(parent);
            }
        },

      async checkPermission(instanceInfo) {
        let viewCode = instanceInfo.viewCode || modelMapviewCode[instanceInfo.masterType];
        let checkPermissionParams = {
          viewCode,
          objectOid: instanceInfo.oid
        };

        try {
          const res = await permissionApi.execute(checkPermissionParams);
          let has = res.find(p => p.code === "details");
          return has && has.status === "enable";
        } catch (err) {
          return false;
        }
      }
    },
};
</script>

<style lang="less" scoped>
.delivery-list-wrap {
    display: flex;
    width: 100%;

    .delivery-panel {
        padding: 16px;

        &.left-panel {
            width: 60vw;
            min-width: 430px;
        }

        &.right-panel {
            flex-grow: 1;
        }
    }

    .ant-radio-button-wrapper {
        width: 32px;
        padding: 0;
        text-align: center;
    }

    /deep/.vxe-table--render-default .vxe-body--column.col--ellipsis,
    /deep/.vxe-table--render-default .vxe-header--column.col--ellipsis {
        height: 40px;
    }

    /deep/.vxe-table--render-default .vxe-body--column:not(.col--ellipsis),
    /deep/.vxe-table--render-default .vxe-footer--column:not(.col--ellipsis),
    /deep/.vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
        padding: 9px 0;

        .vxe-cell {
            padding: 0;
        }
    }

    /deep/.vxe-table .vxe-cell {
        padding: 0 10px;
    }

    /deep/.vxe-table--render-default .vxe-tree--line {
        border-color: #255ed7;
    }

    /deep/.vxe-table--render-default .vxe-tree--node-btn {
        color: #255ed7;
        line-height: 14px;
    }

    /deep/.jw-table .vxe-table.is--tree-line .vxe-body--column,
    /deep/.jw-table .vxe-table.is--tree-line .vxe-header--column {
        border-right: 1px solid #eee;
        border-bottom: 1px solid #eee;
    }

    /deep/.vxe-table .vxe-table--header-wrapper .vxe-table--header-border-line {
        border: 0;
    }

    /deep/.vxe-table--render-default .vxe-table--border-line {
        border-top: 0;
    }

    .number-header-wrap {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        i {
            cursor: pointer;
        }
    }

    .name-wrap {
        display: flex;
        justify-content: space-between;

        .name-con {
            display: flex;
            align-items: center;

            &.name-con-big {
                width: calc(~"100% - 30px");
            }

            &.name-con-small {
                width: calc(~"100% - 10px");
            }

            .name-item {
                margin: 0 8px;
                color: #255ed7;
                cursor: pointer;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .btn-con {
            display: flex;
            justify-content: flex-end;
            visibility: hidden;

            &.btn-con-big {
                min-width: 130px;
            }

            &.btn-con-small {
                min-width: 30px;
            }

            .btn-item {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 24px;
                height: 24px;
                margin-left: 8px;
                background: #FFFFFF;
                border-radius: 4px;
            }
        }

        &:hover {
            .btn-con {
                visibility: visible;
            }
        }
    }

    .level-wrap {
        position: absolute;
        top: 9px;
        right: 0;
        font-size: 12px;
        color: rgba(30, 32, 42, 0.25);
    }

    .no-data-wrap {
        width: 100%;
        height: 100%;

        .no-data-con {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            img {
                width: 193px;
                margin-bottom: 25px;
            }

            span {
                color: #255ed7;
                cursor: pointer;
            }

            i {
                cursor: pointer;
            }

            .ant-btn-link {
                padding: 0;
                vertical-align: middle;
            }
        }
    }
}

.multipane-resizer {
    position: relative;
    margin: 0;
    left: 0;
    background: #eee;

    &:before {
        display: block;
        content: "";
        width: 3px;
        height: 40px;
        position: absolute;
        top: 50%;
        left: 50%;
        margin-top: -20px;
        margin-left: -1.5px;
        border-left: 1px solid #ccc;
        border-right: 1px solid #ccc;
    }

    &:hover {
        &:before {
            border-color: #999;
        }
    }
}
</style>
<style lang="less">
.create-node-class {
    min-width: 160px;

    .ant-dropdown-menu {
        padding: 8px 0;
    }

    .ant-dropdown-menu-item {
        display: flex;
        justify-content: space-between;
        margin: 0 8px;
        padding: 5px 8px;

        i {
            visibility: hidden;
        }

        &:hover {
            border-radius: 4px;

            i {
                visibility: visible;
            }
        }
    }
}
</style>

<style lang="less">
.dialog-class {
    top: 190px;
    margin-left: 900px;
}
</style>
