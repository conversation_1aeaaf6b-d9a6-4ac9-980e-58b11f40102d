<template>
    <div class="structure-wrap" id="importPart">
        <div class="handle-btns" v-if="isEdit">
            <a-button class="handle-btn" type="primary" @click="onSaveTableData">
            保存
        </a-button>
            <a-button class="handle-btn" type="primary" style="margin-right: 10px;" @click="onOpenImportModal({...objectDetailsData, isTop: true})">
                导入
            </a-button>
        </div>
        <div class="table-box">
            <jw-table ref="refTable" :loading="loading" :toolbars="toolbars" :columns="columns" row-id="id" :tree-config="{rowField: 'id', lazy: true, expandRowKeys, loadMethod: loadChildrenMethod}" :data-source.sync="tableData" :showPage="false" :row-style="rowStyle">
                <template #name="{ row }">
                    <div class="name-wrap">
                        <div class="name-con">
                            <jwIcon :type='row.modelIcon'></jwIcon>
                            <div class="name-item">{{ row.number }},{{ row.name }}</div>
                            <jwIcon v-if="row.substitutedFlag" type="#jwi-tidaijian" />
                            <a-dropdown placement="bottomRight" v-if="isEdit&&!row.substitutedFlag">
                                <div style="margin-left:10px">
                                    <jwIcon type="jwi-iconellipsis" style="cursor: pointer"></jwIcon>
                                </div>
                                <a-menu slot="overlay" @click="({ key }) => onRelationOpe(key,row)">
                                    <a-menu-item :key="item.code" v-for="item in getDropDown(row)">
                                        <span :class="item.icon" style="margin-right: 8px"></span>{{ item.name }}
                                    </a-menu-item>
                                </a-menu>
                            </a-dropdown>
                        </div>
                    </div>
                </template>
                <template #quantity="{ row }">
                    <template v-if="isEdit && !row.isTop && !row.substitutedFlag">
                      <a-input-number class="full-width-input" :value="'quantity' in row.use ?
                          row.use.quantity : row.use.extensionContent && 'quantity' in row.use.extensionContent ?
                          row.use.extensionContent.quantity : ''" :min="0"
                          @change="value => {
                              onChangeUses(row, 'quantity', value);
                              debouncedSave();
                          }"
                          @blur="onSaveTableData()" />
                    </template>
                    <template v-else-if="!row.isTop && !row.substitutedFlag">
                      <span>{{ 'quantity' in row.use ?
                          row.use.quantity : row.use.extensionContent && 'quantity' in row.use.extensionContent ?
                          row.use.extensionContent.quantity : '' }}</span>
                    </template>
                </template>
                <template #position="{ row }">
                    <template v-if="isEdit && !row.isTop && !row.substitutedFlag">
                      <a-input :value="'position' in row.use ?
                          row.use.position : row.use.extensionContent && 'position' in row.use.extensionContent ?
                          row.use.extensionContent.position : ''" allowClear
                          :class="{ 'position-error': row.positionError }"
                          @change="value => {
                              onChangeUses(row, 'position', value.target.value);
                              debouncedSave();
                          }"
                          @blur="onSaveTableData()"
                          placeholder="例如：A1, A2, A3" />
                    </template>
                    <template v-else-if="!row.isTop && !row.substitutedFlag">
                      <span>{{ 'position' in row.use ?
                          row.use.position : row.use.extensionContent && 'position' in row.use.extensionContent ?
                          row.use.extensionContent.position : '' }}</span>
                    </template>
                </template>
                <template #unit="{ row }">
                    <template v-if="!row.isTop && !row.substitutedFlag">
                      <span>{{ 'unit' in row.use ?
                          row.use.unit : row.use.extensionContent && 'unit' in row.use.extensionContent ?
                          row.use.extensionContent.unit : '' }}</span>
                    </template>
                </template>
                <template #lineNumber="{ row }">
                    <template v-if="isEdit && !row.isTop && !row.substitutedFlag">
                      <a-input-number class="full-width-input" :value="'lineNumber' in row.use ?
                          row.use.lineNumber : row.use.extensionContent && 'lineNumber' in row.use.extensionContent ?
                          row.use.extensionContent.lineNumber : ''" :precision="0" :min="1"
                          @change="value => {
                              onChangeUses(row, 'lineNumber', value);
                              debouncedSave();
                          }"
                          @blur="onSaveTableData()" />
                    </template>
                    <template v-else-if="!row.isTop && !row.substitutedFlag">
                      <span>{{ 'lineNumber' in row.use ?
                          row.use.lineNumber : row.use.extensionContent && 'lineNumber' in row.use.extensionContent ?
                          row.use.extensionContent.lineNumber : '' }}</span>
                    </template>
                </template>
                <!-- 属性列自定义插槽 -->
                <template #[code]="{ row }" v-for="code in columnSlots">
                    <div :key="code">
                        <template v-if="isEdit && !row.isTop && !row.substitutedFlag">
                            <a-input :value="code in row.use ?
                                row.use[code] : row.use.extensionContent && code in row.use.extensionContent ?
                                row.use.extensionContent[code] : ''" allowClear
                                @change="value => {
                                    onChangeUses(row, code, value.target.value);
                                    debouncedSave();
                                }"
                                @blur="onSaveTableData()" />
                        </template>
                        <template v-else-if="!row.isTop && !row.substitutedFlag">
                            <span>{{ code in row.use ?
                                row.use[code] : row.use.extensionContent && code in row.use.extensionContent ?
                                row.use.extensionContent[code] : '' }}</span>
                        </template>
                    </div>
                </template>
            </jw-table>
        </div>
        <jw-search-engine-modal :title="$t('txt_adding_business_object')" :onlySearchObject="true" :visible.sync="objVisible" :model-list='[
                {
                    name: $t("txt_part"),
                    code: "PartIteration"
                }
            ]' @ok="onAddOk" />
        <create-drawer ref="cerateDrawer" :objectDetailsData="objectDetailsData" @fetchTable="getTableData"></create-drawer>
        <import-modal ref="importModal" :visible.sync="importVisible" :currentTree="objectDetailsData" @renderData="onRenderData"></import-modal>
    </div>
</template>

<script>
import { jwIcon, jwSearchEngineModal } from "jw_frame";
import createDrawer from "views/product-content/content-manage/create-drawer";
import importModal from "./import-modal";
import { generateUUID } from "./apis";
import { getCookie } from "jw_utils/cookie";
import { getParent } from "utils/util.js";
import ModelFactory from "jw_apis/model-factory";
import {
  DEL_FLAG_VALUE,
  MODIFY_FLAG_VALUE,
  ADD_FLAG_VALUE
} from "../flag-config.js";

// 按需获取层级结构树
const fetchUseTree = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/useTree/find`,
  method: "get"
});

// 展示时获取层级结构树
const fetchUseStructure = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/getStructureData`,
  method: "post"
});

// 获取use显示列
const fetchUseCols = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/model/edgeDef/detail`,
  method: "get"
});

// 删除use关系
const deleteUse = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/changeDeleteUse`,
  method: "post"
});

// 获取单位列表
const fetchUnitList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.sysconfigServer}/units/search`,
  method: "get"
});

// 替换
const replaceObj = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/bom/replaceChange`,
  method: "post"
});

// 批量添加子对象
const batchAddUse = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/batchAddChangeUse`,
  method: "post"
});

// 批量更新use关系
const batchUpdateUse = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/batchChangeUpdateUse`,
  method: "post"
});

export default {
  name: "structurePage",
  props: ["objectDetailsData", "isEdit", "beforeData"],
  components: {
    jwIcon,
    jwSearchEngineModal,
    createDrawer,
    importModal
  },
  data() {
    return {
      userOid: Jw.getUser().oid,
      topCatalogOid: "",
      topCatalogType: "",
      loading: false,
      level: 1,
      expandRowKeys: [],
      tableData: [],
      columns: [
        {
          field: "name",
          title: this.$t("txt_name"),
          treeNode: true,
          minWidth: 150,
          slots: {
            default: "name"
          }
        },
        {
          field: "displayVersion",
          title: this.$t("txt_version"),
          minWidth: 30,
          cellRender: {
            name: "tag"
          }
        },
        {
          field: "use.quantity",
          title: this.$t("数量"),
          slots: {
            default: "quantity"
          },
          minWidth: 50,
        },
        {
          field: "use.extensionContent.position",
          title: this.$t("位号"),
          slots: {
            default: "position"
          },
          minWidth: 50,
        },
        {
          field: "use.extensionContent.lineNumber",
          title: this.$t("行号"),
          slots: {
            default: "lineNumber"
          },
          minWidth: 50,
        },
        {
          field: "use.unit",
          title: this.$t("单位"),
          minWidth: 50,
          slots: {
            default: "unit"
          }
        },
      ],
      unitOpts: [],
      objVisible: false,
      importVisible: false,
      detailInfo: {},
      properties: [],
      positionWarningTimer: null, // 位号警告定时器
      // 添加防抖版本的保存函数
      debouncedSave: _.debounce(function() {
        this.onSaveTableData();
      }, 1200)
    };
  },
  computed: {
    toolbars() {
      return [
        // {
        //   name: this.$t("btn_save"),
        //   position: "after",
        //   key: "save",
        //   click: this.onSaveTableData,
        //   disabled: !this.isEdit
        // }
      ];
    },
    // 属性列自定义插槽
    columnSlots() {
      let slots = [];
      this.columns.forEach(col => {
        if (
          this.properties.some(item => item.code == col.code) &&
          ![
            this.$t("txt_num"),
            this.$t("txt_line_number"),
            this.$t("txt_unit")
          ].includes(col.title)
        ) {
          slots = slots.concat(
            Object.values(col.slots).filter(slot => typeof slot === "string")
          );
        }
      });
      return slots;
    }
  },
  mounted() {
    this.init();
  },
  beforeDestroy() {
    // 清理定时器
    if (this.positionWarningTimer) {
      clearTimeout(this.positionWarningTimer);
    }
  },
  methods: {
    init() {
      this.getUseCols();
      this.getUnitList();
    },
    onSearch() {
      this.getTableData();
    },
    getDropDown(row) {
      const { isTop } = row;
      if (isTop) {
        return [
          {
            code: "createPart",
            icon: "jwi-iconsubItem-add",
            modelType: "PartIteration",
            name: this.$t("txt_add_part")
          },
          {
            code: "createHasPart",
            icon: "jwi-iconSelectobject",
            modelType: "PartIteration",
            name: this.$t("txt_add_existing_part")
          },
          {
            code: "importPart",
            icon: "jwi-iconImport",
            modelType: "PartIteration",
            name: this.$t("btn_import")
          }
        ];
      } else {
        return [
          {
            code: "remove",
            icon: "jwi-icondelete",
            modelType: "PartIteration",
            name: this.$t("txt_remove")
          },
          {
            code: "replace",
            icon: "jwi-iconreplace",
            modelType: "PartIteration",
            name: this.$t("txt_replacement")
          }
        ];
      }
    },
    getUseCols() {
      fetchUseCols
        .execute({
          fromCode: this.objectDetailsData.modelDefinition,
          toCode: "Part",
          relCode: "USE"
        })
        .then(res => {
          if (res && res.properties && res.properties.length > 0) {
            res.properties.forEach(item => {
              this.columns.push({
                code: item.code,
                title: item.description,
                minWidth: 140,
                slots: {
                  default: item.code
                }
              });
            });
            this.properties = res.properties;
          } else {
            this.properties = [];
          }
          this.onSearch();
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },
    getTableData() {
      this.loading = true;
      let api = fetchUseTree;
      let params = {
        searchKey: "",
        rootOid: this.objectDetailsData.oid,
        // maxLevel: 99
        maxLevel: 1 //暂时将PCN的 物料的BOM查询层级设置为1
      };
      if (!this.isEdit) {
        api = fetchUseStructure;
        params = {
          primaryOid: this.beforeData.oid,
          targetOid: this.objectDetailsData.oid
        };
      }
      api
        .execute(params)
        .then(res => {
          generateUUID(res);
          this.setInitStatus(res, 0);
          this.tableData = res;
          this.loading = false;
        })
        .catch(err => {
          this.loading = false;
          this.$error(err.msg);
        });
    },
    setInitStatus(tree, rowLevel) {
      const loop = (data, level) => {
        data.forEach(val => {
          val.isEdit = false;
          val.unitOpts = this.unitOpts;
          val.level = level;
          let parent = getParent(tree, val.id, "id");
          val.parentOid = parent ? parent.oid : val.oid;
          val.fatherLockOwnerOid = parent ? parent.lockOwnerOid : "";
          if (!parent) {
            val.isTop = true;
            this.topCatalogOid = val.catalogOid;
            this.topCatalogType = val.catalogType;
          }
          if (!val.use) {
            val.use = {
              extensionContent: {}
            };
          } else if (!val.use.extensionContent) {
            val.use.extensionContent = {};
          }
          this.properties.forEach(item => {
            val.use.extensionContent = val.use.extensionContent
              ? val.use.extensionContent
              : {};
            val["is" + item.code] = false;
          });
          val.cloneData = _.cloneDeep(val);
          if (val.children instanceof Array) {
            loop(val.children, level + 1);
          }
          if (level >= this.level) {
            // val.hasChild = true;
          } else {
            this.expandRowKeys.push(val.id);
          }
        });
      };
      loop(tree, rowLevel);
    },
    loadChildrenMethod({ row }) {
      if (row.substitutedFlag) {
        return new Promise((resolve, reject) => {
          resolve();
        });
      }
      return fetchUseTree
        .execute({
          rootOid: row.oid,
          maxLevel: 1
        })
        .then(res => {
          if (res[0].children.length === 0) {
            row.hasChild = false;
          }
          generateUUID(res);
          this.setInitStatus(res, row.level);
          return res[0].children;
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },
    rowStyle({ row }) {
      if (row.isEdit || row.flag === MODIFY_FLAG_VALUE) {
        return {
          backgroundColor: "#fffaf0"
        };
      } else if (row.flag === ADD_FLAG_VALUE) {
        return {
          backgroundColor: "#CDEEB0"
        };
      } else if (row.flag === DEL_FLAG_VALUE) {
        return {
          backgroundColor: "#FFC2C3"
        };
      }
    },
    onRelationOpe(code, row) {
      switch (code) {
        case "createPart":
          this.onOpenCreateModal(row);
          break;
        case "createHasPart":
          this.pageCode = "";
          this.onOpenObjModal(row);
          break;
        case "remove":
          this.onDeleteUse(row);
          break;
        case "replace":
          this.pageCode = "structurePage";
          this.onOpenObjModal(row);
          break;
        case "importPart":
          this.onOpenImportModal(row);
          break;
        default:
          break;
      }
    },
    onDeleteUse(row) {
      this.$confirm({
        title: this.$t("txt_comfirm_remove"),
        okText: this.$t("btn_ok"),
        cancelText: this.$t("btn_cancel"),
        onOk: () => {
          return deleteUse
            .execute(row.use)
            .then(res => {
              this.$success(this.$t("txt_remove_success"));
              this.onSearch();
            })
            .catch(err => {
              if (err.msg) {
                this.$error(err.msg);
              }
            });
        }
      });
    },
    onOpenObjModal(row) {
      this.objVisible = true;
      this.detailInfo = { ...row };
    },
    onAddOk(selectedRows) {
      if (selectedRows.length === 0) {
        this.$warning(this.$t("txt_please_seleted_data"));
        return;
      }
      if (!this.pageCode) {
        let selected = [];
        selectedRows.forEach(item => {
          let temp = {
            fromOid: this.detailInfo.oid,
            fromType: this.detailInfo.type,
            toOid: item.oid,
            toType: item.type,
            quantity: 1
          };
          selected.push(temp);
        });
        batchAddUse
          .execute(selected)
          .then(res => {
            this.$success(this.$t("msg_save_success"));
            this.onCloseObjModal();
            this.onSearch();
          })
          .catch(err => {
            this.$error(err.msg);
          });
      } else {
        let treeData = this.$refs.refTable.getTableData().fullData;
        let parent = getParent(treeData, this.detailInfo.id, "id");
        let params = {
          newPartOid: selectedRows[0].oid,
          originalPartOid: this.detailInfo.oid,
          parentOid: parent.oid
        };
        replaceObj
          .execute(params)
          .then(res => {
            this.$success(this.$t("msg_success"));
            this.onCloseObjModal();
            this.onSearch();
          })
          .catch(err => {
            this.$error(err.msg);
          });
      }
    },
    onCloseObjModal() {
      this.objVisible = false;
    },
    onOpenCreateModal(row) {
      this.$refs.cerateDrawer.show({
        title: this.$t("txt_reate_part"),
        modelInfo: {
          layoutName: "create",
          modelName: "Part"
        },
        params: {
          targetOid: row.oid,
          targetType: row.type,
          url: `${Jw.gateway}/${
            Jw.partBomMicroServer
          }/part/createChangeThenUse`,
          locationInfo: {
            catalogOid: this.topCatalogOid,
            catalogType: this.topCatalogType,
            containerOid: row.containerOid,
            containerType: row.containerType,
            containerModelDefinition: row.containerModelDefinition
          }
        }
      });
    },
    getUnitList() {
      fetchUnitList
        .execute({
          currentPage: 1,
          size: 1000,
          containerOid: getCookie("tenantOid")
        })
        .then(res => {
          this.unitOpts = res.rows;
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },
    onChangeUses(row, key, value) {
      let updateData = "";
      let oriData = "";
      if (key in row.use) {
        this.$set(row, "use", { ...row.use, [key]: value });
        updateData = row.use[key];
        oriData = row.cloneData.use[key];
      } else {
        // 确保extensionContent存在
        if (!row.use.extensionContent) {
          row.use.extensionContent = {};
        }
        this.$set(row, "use", {
          ...row.use,
          extensionContent: { ...row.use.extensionContent, [key]: value }
        });
        updateData = row.use.extensionContent[key];
        oriData = row.cloneData.use && row.cloneData.use.extensionContent ?
                 row.cloneData.use.extensionContent[key] : '';
      }

      // 如果修改的是位号，进行实时校验
      if (key === 'position' && value && value.trim() !== '') {
        this.validatePositionInput(row, value);
      }

      if (updateData !== oriData) {
        row.isEdit = true;
      } else {
        row.isEdit = false;
      }
    },

    // 校验位号格式
    validatePositionFormat(position) {
      if (!position || position.trim() === '') return null;

      const trimmedPosition = position.trim();

      // 检查是否以逗号结尾
      if (trimmedPosition.endsWith(',')) {
        return '位号不能以逗号结尾';
      }

      // 检查是否以逗号开头
      if (trimmedPosition.startsWith(',')) {
        return '位号不能以逗号开头';
      }

      // 检查是否有连续的逗号
      if (trimmedPosition.includes(',,')) {
        return '位号不能包含连续的逗号';
      }

      // 检查位号格式：应该是 "A1, A2, A3" 的格式
      // 正确格式：字母数字 + 逗号 + 空格 + 字母数字...
      const correctFormatRegex = /^[A-Za-z0-9]+(\s*,\s*[A-Za-z0-9]+)*$/;
      if (!correctFormatRegex.test(trimmedPosition)) {
        return '位号格式应为 "A1, A2, A3"（英文逗号+空格分隔）';
      }

      // 检查逗号后是否有空格（严格格式检查）
      const parts = trimmedPosition.split(',');
      for (let i = 0; i < parts.length; i++) {
        const part = parts[i];

        // 第一个部分前面不应该有空格，后面部分前面应该有空格
        if (i === 0) {
          if (part !== part.trim()) {
            return '第一个位号前不应有空格';
          }
        } else {
          if (!part.startsWith(' ')) {
            return '逗号后应有一个空格';
          }
          if (part.trim() !== part.substring(1)) {
            return '位号前后不应有多余空格';
          }
        }

        // 检查每个位号是否为空
        if (part.trim() === '') {
          return '位号不能为空';
        }
      }

      return null; // 格式正确
    },

    // 位号输入实时校验
    validatePositionInput(row, positionValue) {
      // 清除之前的错误状态
      this.$set(row, 'positionError', false);

      if (!positionValue || positionValue.trim() === '') return;

      const errors = [];

      // 检查位号整体格式
      const formatError = this.validatePositionFormat(positionValue);
      if (formatError) {
        errors.push(formatError);
      } else {
        // 格式正确时才进行其他校验
        const positions = positionValue.split(',').map(p => p.trim()).filter(p => p !== '');

        // 检查位号内容格式
        const positionRegex = /^[A-Za-z0-9]+$/;
        const invalidPositions = positions.filter(p => !positionRegex.test(p));
        if (invalidPositions.length > 0) {
          errors.push(`位号内容不正确：${invalidPositions.join(', ')}`);
        }

        // 检查当前行内的重复（局部重复检查）
        const uniquePositions = [...new Set(positions)];
        if (positions.length !== uniquePositions.length) {
          const duplicates = positions.filter((item, index) => positions.indexOf(item) !== index);
          errors.push(`当前行位号重复：${[...new Set(duplicates)].join(', ')}`);
        }

        // 检查与数量的匹配
        const quantity = ('quantity' in row.use ?
          row.use.quantity :
          row.use.extensionContent && 'quantity' in row.use.extensionContent ?
          row.use.extensionContent.quantity : '');

        if (quantity && positions.length !== Number(quantity)) {
          errors.push(`位号数=${positions.length}与数量=${quantity}不匹配`);
        }
      }

      // 设置错误状态
      if (errors.length > 0) {
        this.$set(row, 'positionError', true);
        // 延迟显示警告，避免输入时频繁弹出
        clearTimeout(this.positionWarningTimer);
        this.positionWarningTimer = setTimeout(() => {
          this.$warning(`位号校验警告：\n${errors.join('\n')}\n\n`);
        }, 1000);
      }
    },
    getEditData(tree) {
      let editData = [];
      const loop = data => {
        data.forEach(val => {
          if (val.isEdit) {
            editData.push(val.use);
          }
          if (val.children instanceof Array) {
            loop(val.children);
          }
        });
      };
      loop(tree);
      return editData;
    },
    // 校验位号和数量
    validatePositionAndQuantity() {
      const allData = this.$refs.refTable.getTableData().fullData;
      const quantityMismatchErrors = [];
      const formatErrors = [];
      const globalPositionMap = new Map(); // 全局位号映射：位号 -> [部件列表]

      const collectAndValidateRow = (row) => {
        // 跳过顶级节点和替代件
        if (row.isTop || row.substitutedFlag) return;

        // 获取位号和数量
        const position = ('position' in row.use ?
          row.use.position :
          row.use.extensionContent && 'position' in row.use.extensionContent ?
          row.use.extensionContent.position : '');

        const quantity = ('quantity' in row.use ?
          row.use.quantity :
          row.use.extensionContent && 'quantity' in row.use.extensionContent ?
          row.use.extensionContent.quantity : '');

        // 如果位号为空，不需要校验
        if (!position || position.trim() === '') return;

        // 校验位号整体格式
        const formatError = this.validatePositionFormat(position);
        if (formatError) {
          formatErrors.push(`部件 "${row.number},${row.name}" 的位号格式错误：${formatError}`);
          return; // 格式错误时不继续后续校验
        }

        // 解析位号：按英文逗号分割，去除空格
        const positions = position.split(',').map(p => p.trim()).filter(p => p !== '');

        // 校验位号内容格式
        const positionRegex = /^[A-Za-z0-9]+$/;
        const invalidPositions = positions.filter(p => !positionRegex.test(p));
        if (invalidPositions.length > 0) {
          formatErrors.push(`部件 "${row.number},${row.name}" 的位号内容不正确：${invalidPositions.join(', ')}`);
        }

        // 校验位号数量与数量字段匹配
        if (positions.length !== Number(quantity)) {
          quantityMismatchErrors.push(`${row.number}（数量=${quantity}，位号数=${positions.length}）`);
        }

        // 收集位号信息用于全局重复检查
        positions.forEach(pos => {
          if (!globalPositionMap.has(pos)) {
            globalPositionMap.set(pos, []);
          }
          globalPositionMap.get(pos).push(row.number);
        });
      };

      // 递归收集所有行的数据
      const collectTree = (data) => {
        data.forEach(row => {
          collectAndValidateRow(row);
          if (row.children && row.children.length > 0) {
            collectTree(row.children);
          }
        });
      };

      collectTree(allData);

      // 检查全局位号重复
      const duplicateErrors = [];
      globalPositionMap.forEach((partNumbers, position) => {
        if (partNumbers.length > 1) {
          duplicateErrors.push(`${position}（${partNumbers.join(', ')}）`);
        }
      });

      // 组装最终错误信息
      const finalErrors = [];

      if (formatErrors.length > 0) {
        finalErrors.push(...formatErrors);
      }

      if (quantityMismatchErrors.length > 0) {
        finalErrors.push('位号与数量不匹配：\n' + quantityMismatchErrors.join('\n'));
      }

      if (duplicateErrors.length > 0) {
        finalErrors.push('位号重复：\n' + duplicateErrors.join('\n'));
      }

      return finalErrors;
    },

    // 自动修正位号格式
    autoCorrectPositions() {
      const allData = this.$refs.refTable.getTableData().fullData;
      let corrected = false;

      const correctRow = (row) => {
        if (row.isTop || row.substitutedFlag) return;

        const position = ('position' in row.use ?
          row.use.position :
          row.use.extensionContent && 'position' in row.use.extensionContent ?
          row.use.extensionContent.position : '');

        if (position && position.trim() !== '') {
          // 自动修正：去除首尾空格，标准化格式
          const correctedPosition = position
            .trim() // 去除首尾空格
            .replace(/\s*,\s*/g, ', ') // 标准化逗号空格格式
            .replace(/,\s*$/, ''); // 去除末尾的逗号

          if (correctedPosition !== position) {
            // 更新位号
            if ('position' in row.use) {
              row.use.position = correctedPosition;
            } else if (row.use.extensionContent) {
              row.use.extensionContent.position = correctedPosition;
            }
            corrected = true;
          }
        }
      };

      const correctTree = (data) => {
        data.forEach(row => {
          correctRow(row);
          if (row.children && row.children.length > 0) {
            correctTree(row.children);
          }
        });
      };

      correctTree(allData);
      return corrected;
    },

    onSaveTableData() {
      // 自动修正位号格式
      const wasCorrected = this.autoCorrectPositions();
      if (wasCorrected) {
        this.$message.info('已自动修正位号格式（去除多余空格和末尾逗号）');
      }

      // 进行校验
      const validationErrors = this.validatePositionAndQuantity();
      if (validationErrors.length > 0) {
        this.$error(`保存失败：\n${validationErrors.join('\n')}\n\n`);
        return;
      }

      let data = this.getEditData(this.$refs.refTable.getTableData().fullData);
      if (data.length === 0) return;
      batchUpdateUse
        .execute(data)
        .then(res => {
          this.$success(this.$t("msg_save_success"));
          this.onSearch();
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },
    onOpenImportModal(row) {
      this.importVisible = true;
      this.detailInfo = { ...row };
    },
    onRenderData() {
      this.onSearch();
    }
  }
};
</script>

<style lang="less" scoped>
.structure-wrap {
  height: 100%;
  padding: 10px;
  .handle-btn {
    margin-left: 10px;
  }
  .handle-btns {
    position: absolute;
    right: 10px;
    top: 6px;
    display: flex;
    flex-direction: row-reverse;
  }
  .table-box {
    height: 100%;
  }
  .name-wrap {
    // display: flex;
    // justify-content: space-between;
    .name-con {
      display: flex;
      align-items: center;
      .name-item {
        max-width: 85%;
        margin-right: 5px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #255ed7;
        cursor: pointer;
      }
    }
  }
  .ant-select {
    width: 100%;
  }
  .full-width-input {
    width: 100%;
  }

  // 位号错误样式
  .position-error {
    border-color: #ff4d4f !important;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
  }
}
</style>
