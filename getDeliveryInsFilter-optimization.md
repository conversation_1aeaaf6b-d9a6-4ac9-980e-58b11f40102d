# getDeliveryInsFilter 方法优化说明

## 优化前的问题

1. **频繁的网络请求**：每次点击下拉菜单都会发起权限查询请求
2. **重复查询**：相同的节点被多次查询，浪费资源
3. **用户体验差**：每次都显示loading状态，响应慢
4. **代码冗余**：权限处理逻辑分散，难以维护

## 优化方案

### 1. 添加权限缓存机制

```javascript
// 在data中添加权限缓存
permissionCache: {}, // 权限缓存

// 生成缓存key
const cacheKey = row.modelDefinition !== 'Structure' && row.modelDefinition !== 'Category' 
    ? `${row.parentOid}_${row.parentType}` 
    : `${row.oid}_${row.type}`;

// 检查缓存
if (this.permissionCache && this.permissionCache[cacheKey]) {
    this.setRowOperationList(row, this.permissionCache[cacheKey]);
    return;
}
```

### 2. 避免重复查询

```javascript
// 如果已经加载过权限，直接返回
if (row.operationList && row.operationList.length > 0) {
    return;
}
```

### 3. 提取权限处理逻辑

将权限列表的设置逻辑提取到独立的方法 `setRowOperationList`：

```javascript
setRowOperationList(row, permissions) {
    let operationList = [];
    
    if (row.modelDefinition !== 'Structure' && row.modelDefinition !== 'Category') {
        // 实例对象只显示删除操作
        operationList = permissions.filter(item => item.code === 'delete');
        
        // 如果是产品目录类型，禁用删除
        if (this.spectrumInfo.type === 'ProductCatalog' && operationList.length > 0) {
            operationList[0].status = 'disable';
        }
    } else {
        // 结构或类型节点，过滤相关操作
        operationList = permissions.filter(item => {
            // 始终显示编辑和删除
            if (item.code === 'edit' || item.code === 'delete') {
                return true;
            }
            // 根据节点类型过滤其他操作
            return item.categoryType === row.modelDefinition;
        });
    }
    
    this.$set(row, 'operationList', operationList);
    this.$set(row, 'loading_status', 'done');
}
```

### 4. 缓存清理机制

在数据发生变化时清理缓存：

```javascript
// 清理权限缓存
clearPermissionCache() {
    this.permissionCache = {};
}

// 在数据变化的方法中调用
onSearch() {
    this.clearPermissionCache();
    this.getTableData();
}

reRenderTable() {
    this.clearPermissionCache();
    // ... 其他逻辑
}
```

## 优化效果

1. **性能提升**：相同节点的权限查询只执行一次
2. **用户体验改善**：已查询过的节点立即显示，无loading状态
3. **网络请求减少**：大幅减少不必要的权限查询请求
4. **代码可维护性**：权限处理逻辑集中，易于维护和扩展

## 缓存策略

- **缓存Key**：根据节点类型和OID生成唯一标识
- **缓存时机**：首次查询权限时缓存结果
- **缓存清理**：数据变化时清理所有缓存
- **缓存范围**：当前组件实例内有效

## 注意事项

1. 缓存在组件销毁时自动清理
2. 数据变化时需要手动清理缓存
3. 缓存key的生成需要保证唯一性
4. 权限变化时需要清理相关缓存
