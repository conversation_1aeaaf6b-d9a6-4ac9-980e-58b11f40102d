# 批量更新PDM交付清单到PMS接口伪代码

## 接口信息
- **URL**: `/delivery/batchUpdateToPMS`
- **Method**: POST
- **描述**: 批量更新PDM交付清单到PMS中预期文档的状态

## 请求参数
```json
{
  "catalogOid": "string",     // 目录OID
  "catalogType": "string",    // 目录类型
  "searchKey": "string",      // 搜索关键字（可选）
  "level": "number"           // 层级（可选，null表示所有层级）
}
```

## 响应格式
```json
{
  "success": true,
  "message": "批量更新成功",
  "data": {
    "totalCount": 100,        // 总数量
    "successCount": 95,       // 成功数量
    "failedCount": 5,         // 失败数量
    "failedItems": [          // 失败的项目列表
      {
        "oid": "xxx",
        "name": "文档名称",
        "reason": "失败原因"
      }
    ]
  }
}
```

## 伪代码实现

```java
@RestController
@RequestMapping("/delivery")
public class DeliveryController {
    
    @PostMapping("/batchUpdateToPMS")
    public ResponseEntity<ApiResponse> batchUpdateToPMS(@RequestBody BatchUpdateRequest request) {
        try {
            // 1. 权限验证
            if (!hasPermission(request.getCatalogOid(), "DELIVERYCONTAINEROPERATION", "batchUpdateToPMS")) {
                return ResponseEntity.status(403).body(ApiResponse.error("无权限执行此操作"));
            }
            
            // 2. 根据条件查询交付清单数据
            List<DeliveryItem> deliveryItems = deliveryService.findDeliveryItems(
                request.getCatalogOid(),
                request.getCatalogType(),
                request.getSearchKey(),
                request.getLevel()
            );
            
            // 3. 批量更新到PMS
            BatchUpdateResult result = new BatchUpdateResult();
            result.setTotalCount(deliveryItems.size());
            
            for (DeliveryItem item : deliveryItems) {
                try {
                    // 调用PMS接口更新预期文档状态
                    pmsService.updateExpectedDocumentStatus(item);
                    result.incrementSuccessCount();
                } catch (Exception e) {
                    result.incrementFailedCount();
                    result.addFailedItem(item.getOid(), item.getName(), e.getMessage());
                    log.error("更新项目失败: " + item.getOid(), e);
                }
            }
            
            // 4. 返回结果
            return ResponseEntity.ok(ApiResponse.success("批量更新完成", result));
            
        } catch (Exception e) {
            log.error("批量更新到PMS失败", e);
            return ResponseEntity.status(500).body(ApiResponse.error("批量更新失败: " + e.getMessage()));
        }
    }
    
    // 权限检查方法
    private boolean hasPermission(String catalogOid, String viewCode, String operation) {
        // 调用权限服务检查用户是否有指定操作权限
        return permissionService.checkPermission(getCurrentUser(), viewCode, catalogOid, operation);
    }
}

// 请求对象
class BatchUpdateRequest {
    private String catalogOid;
    private String catalogType;
    private String searchKey;
    private Integer level;
    // getters and setters...
}

// 结果对象
class BatchUpdateResult {
    private int totalCount;
    private int successCount;
    private int failedCount;
    private List<FailedItem> failedItems = new ArrayList<>();
    // getters and setters...
}

class FailedItem {
    private String oid;
    private String name;
    private String reason;
    // getters and setters...
}
```

## 权限配置
需要在权限系统中配置 `DELIVERYCONTAINEROPERATION` 视图下的 `batchUpdateToPMS` 操作权限。

## 注意事项
1. 需要确保PMS系统接口的可用性
2. 建议添加事务处理，确保数据一致性
3. 可以考虑异步处理大批量数据
4. 添加操作日志记录
5. 考虑添加进度回调机制
